<template>
  <div :class="{ 'binactive: !active, 'cursor-not-allowed': !active, 'no-pointer-events': !active }">
    <p>{{ title }}</p>
    <ul>
      <li v-for="todo in todos" :key="todo.id" :class="{ 'text-strike': todo.completed }">
        <label>
          <input type="checkbox" :checked="todo.completed" @change="todo.toggle()" />
          {{ todo.content }}
        </label>
      </li>
    </ul>
    <p>Count: {{ todoCount }} / {{ meta.totalCount }}</p>
    <p>Active: {{ active ? 'yes' : 'no' }}</p>
    <p class="non-selectable" @click="increment">Clicks on this: {{ clickCount }}</p>
    <button @click="clickCount = 0">Reset Count</button>
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue';
import type { Todo } from '../models/Todo'
import type { Meta } from '../models/Meta';

interface Props {
  title: string;
  todos?: Todo[];
  meta: Meta;
  active: boolean;
};

const props = withDefaults(defineProps<Props>(), {
  todos: () => []
});

const clickCount = ref(0);
function increment() {
  clickCount.value += 1;
  return clickCount.value;
}

const todoCount = computed(() => props.todos.length);
</script>

<style scoped>
.inactive {
  opacity: 0.5;
}

ul {
  list-style: none;
  padding: 0;

  li input {
    margin-inline-end: 1ch;
  }
}
</style>
